"use client";
import { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  TableCell,
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
} from "@nextui-org/react";
import { But<PERSON> } from "@heroui/button";
import { Spinner } from "@heroui/spinner";

import CreateModalContact from "./create-modal-contact";

import { useContacts, Contact } from "@/hooks/contacts/useContacts";

interface ContactsModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedProject: number | null;
}

export const ContactsModal = ({
  isOpen,
  onClose,
  selectedProject,
}: ContactsModalProps) => {
  const {
    contacts: projectContacts,
    loading: loadingContacts,
    error: contactsError,
    fetchContactsByProject,
  } = useContacts();

  const [contacts, setContacts] = useState<Contact[]>([]);
  const [createModalOpen, setCreateModalOpen] = useState(false);

  const handleAddContact = (newContact: any) => {
    setContacts((prev) => [...prev, newContact]);
  };

  useEffect(() => {
    if (!isOpen) return;
    if (!selectedProject) return;
    fetchContactsByProject(selectedProject || 0);
  }, [selectedProject, isOpen]);

  useEffect(() => {
    if (projectContacts) {
      setContacts(projectContacts);
    }
  }, [projectContacts, isOpen]);

  return (
    <>
      <Modal isDismissable={false} isOpen={isOpen} size="3xl" onClose={onClose}>
        <ModalContent>
          {(onClose) => (
            <>
              <ModalHeader className="flex flex-col gap-1">
                <div className="flex justify-between items-center w-full">
                  <h2>Contactos</h2>
                </div>
              </ModalHeader>
              <ModalBody>
                <Table removeWrapper selectionMode="single">
                  <TableHeader>
                    <TableColumn>LID - Razón Social</TableColumn>
                    <TableColumn>Tipo de contacto</TableColumn>
                    <TableColumn>Cargo</TableColumn>
                    <TableColumn>Nombre</TableColumn>
                    <TableColumn>Correo</TableColumn>
                  </TableHeader>
                  <TableBody
                    isLoading={loadingContacts}
                    loadingContent={
                      <div>
                        <Spinner size="md" />
                      </div>
                    }
                    emptyContent={
                      contactsError ? (
                        <div className="text-red-500">
                          Error al cargar los contactos: {contactsError}
                        </div>
                      ) : (
                        <div>No hay contactos disponibles.</div>
                      )
                    }
                  >
                    {contacts.map((contact, index) => (
                      <TableRow key={index}>
                        <TableCell>{contact.project}</TableCell>
                        <TableCell>{contact.type}</TableCell>
                        <TableCell>{contact.position}</TableCell>
                        <TableCell>{contact.name}</TableCell>
                        <TableCell className="underline">
                          <a href={`mailto:${contact.email}`}>
                            {contact.email}
                          </a>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </ModalBody>
              <ModalFooter className="flex justify-between">
                <Button
                  color="primary"
                  onPress={() => setCreateModalOpen(true)}
                >
                  Crear Contacto
                </Button>
                <Button color="primary" onPress={onClose}>
                  Cerrar
                </Button>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>

      {/* Create Contact Modal */}
      <Modal
        isDismissable={false}
        isOpen={createModalOpen}
        onClose={() => setCreateModalOpen(false)}
      >
        <ModalContent>
          <CreateModalContact
            projectId={selectedProject}
            onAddContact={handleAddContact}
            onClose={() => setCreateModalOpen(false)}
          />
        </ModalContent>
      </Modal>
    </>
  );
};
