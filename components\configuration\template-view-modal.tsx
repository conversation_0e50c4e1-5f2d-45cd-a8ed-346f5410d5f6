import React, { useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>bs,
  <PERSON>b,
  <PERSON>,
  Spinner,
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
} from "@heroui/react";
import { Icon } from "@iconify/react";

import { useTemplateDetail } from "@/hooks/templates/useTemplateDetail";

interface Template {
  id: number;
  title: string;
  description: string;
  is_active: boolean;
}

interface TemplateViewModalProps {
  isOpen: boolean;
  template: Template | null;
  onClose: () => void;
}

export function TemplateViewModal({
  isOpen,
  template,
  onClose,
}: TemplateViewModalProps) {
  const {
    templateDetail,
    loading,
    error,
    fetchTemplateDetail,
    clearTemplateDetail,
  } = useTemplateDetail();

  useEffect(() => {
    if (isOpen && template) {
      fetchTemplateDetail(template.id.toString());
    } else if (!isO<PERSON>) {
      clearTemplateDetail();
    }
  }, [isOpen, template]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("es-ES", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const renderFields = () => {
    if (!templateDetail?.full_definition?.fields?.length) {
      return (
        <div className="text-center py-8">
          <Icon
            className="text-4xl text-default-400 mb-2"
            icon="lucide:folder-open"
          />
          <p className="text-default-500">No hay campos asociados</p>
        </div>
      );
    }

    return (
      <Table removeWrapper aria-label="Campos de la plantilla">
        <TableHeader>
          <TableColumn>Nombre</TableColumn>
          <TableColumn>Tipo</TableColumn>
          <TableColumn>Descripción</TableColumn>
          <TableColumn>Subfase</TableColumn>
          <TableColumn>Peso</TableColumn>
          <TableColumn>Hito</TableColumn>
        </TableHeader>
        <TableBody>
          {templateDetail.full_definition.fields.map((field) => (
            <TableRow key={field.id}>
              <TableCell>{field.name}</TableCell>
              <TableCell>
                <Chip color="primary" size="sm" variant="flat">
                  {field.type}
                </Chip>
              </TableCell>
              <TableCell>{field.description}</TableCell>
              <TableCell>{field.subphase}</TableCell>
              <TableCell>{field.weight}</TableCell>
              <TableCell>
                {field.is_milestone ? (
                  <Chip color="warning" size="sm" variant="flat">
                    Hito
                  </Chip>
                ) : (
                  "-"
                )}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    );
  };

  const renderRules = () => {
    if (!templateDetail?.full_definition?.rules?.length) {
      return (
        <div className="text-center py-8">
          <Icon
            className="text-4xl text-default-400 mb-2"
            icon="lucide:folder-open"
          />
          <p className="text-default-500">No hay reglas asociadas</p>
        </div>
      );
    }

    return (
      <Table removeWrapper aria-label="Reglas de la plantilla">
        <TableHeader>
          <TableColumn>Nombre</TableColumn>
          <TableColumn>Acción</TableColumn>
          <TableColumn>Descripción</TableColumn>
          <TableColumn>Campo origen</TableColumn>
          <TableColumn>Campo destino</TableColumn>
          <TableColumn>Condición</TableColumn>
        </TableHeader>
        <TableBody>
          {templateDetail.full_definition.rules.map((rule) => (
            <TableRow key={rule.id}>
              <TableCell>{rule.name}</TableCell>
              <TableCell>
                <Chip color="secondary" size="sm" variant="flat">
                  {rule.action}
                </Chip>
              </TableCell>
              <TableCell>{rule.description}</TableCell>
              <TableCell>{rule.origin_field}</TableCell>
              <TableCell>{rule.target_field}</TableCell>
              <TableCell>{rule.condition}</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    );
  };

  const renderNotifications = () => {
    if (!templateDetail?.full_definition?.notifications?.length) {
      return (
        <div className="text-center py-8">
          <Icon
            className="text-4xl text-default-400 mb-2"
            icon="lucide:folder-open"
          />
          <p className="text-default-500">No hay notificaciones asociadas</p>
        </div>
      );
    }

    return (
      <Table removeWrapper aria-label="Notificaciones de la plantilla">
        <TableHeader>
          <TableColumn>Nombre</TableColumn>
          <TableColumn>Descripción</TableColumn>
          <TableColumn>Campo disparador</TableColumn>
          <TableColumn>Condición</TableColumn>
          <TableColumn>Valor</TableColumn>
        </TableHeader>
        <TableBody>
          {templateDetail.full_definition.notifications.map((notification) => (
            <TableRow key={notification.id}>
              <TableCell>{notification.name}</TableCell>
              <TableCell>{notification.description}</TableCell>
              <TableCell>{notification.trigger_field}</TableCell>
              <TableCell>{notification.trigger_condition}</TableCell>
              <TableCell>{notification.value}</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    );
  };

  return (
    <Modal
      classNames={{
        base: "max-h-[90vh]",
        body: "max-h-[calc(90vh-120px)] overflow-auto p-0",
      }}
      isOpen={isOpen}
      scrollBehavior="inside"
      size="5xl"
      onClose={onClose}
    >
      <ModalContent>
        {(onClose) => (
          <>
            <ModalHeader className="flex flex-col gap-1 pb-2 border-b">
              <div className="flex justify-between items-start">
                <div>
                  <h3 className="text-xl font-medium">{template?.title}</h3>
                  {template?.description && (
                    <p className="text-small text-default-500 mt-1">
                      {template.description}
                    </p>
                  )}
                </div>
                <Chip
                  color={template?.is_active ? "success" : "default"}
                  size="sm"
                  variant="flat"
                >
                  {template?.is_active ? "Activa" : "Inactiva"}
                </Chip>
              </div>
              {templateDetail && (
                <div className="flex gap-4 text-xs text-default-500 mt-2">
                  <span>Creado: {formatDate(templateDetail.created_at)}</span>
                  <span>
                    Actualizado: {formatDate(templateDetail.updated_at)}
                  </span>
                </div>
              )}
            </ModalHeader>
            <ModalBody className="p-6">
              {loading ? (
                <div className="flex justify-center items-center py-12">
                  <Spinner size="lg" />
                </div>
              ) : error ? (
                <div className="text-center py-12">
                  <Icon
                    className="text-4xl text-danger mb-2"
                    icon="lucide:alert-circle"
                  />
                  <p className="text-danger">
                    Error al cargar los detalles: {error}
                  </p>
                </div>
              ) : (
                <Tabs aria-label="Template details" className="w-full">
                  <Tab
                    key="fields"
                    title={
                      <div className="flex items-center space-x-2">
                        <Icon icon="lucide:list" />
                        <span>
                          Campos (
                          {templateDetail?.full_definition?.fields?.length || 0}
                          )
                        </span>
                      </div>
                    }
                  >
                    <div className="">{renderFields()}</div>
                  </Tab>
                  <Tab
                    key="rules"
                    title={
                      <div className="flex items-center space-x-2">
                        <Icon icon="lucide:settings" />
                        <span>
                          Reglas (
                          {templateDetail?.full_definition?.rules?.length || 0})
                        </span>
                      </div>
                    }
                  >
                    <div className="">{renderRules()}</div>
                  </Tab>
                  <Tab
                    key="notifications"
                    title={
                      <div className="flex items-center space-x-2">
                        <Icon icon="lucide:bell" />
                        <span>
                          Notificaciones (
                          {templateDetail?.full_definition?.notifications
                            ?.length || 0}
                          )
                        </span>
                      </div>
                    }
                  >
                    <div className="">{renderNotifications()}</div>
                  </Tab>
                </Tabs>
              )}
            </ModalBody>
            <ModalFooter>
              <Button variant="light" onPress={onClose}>
                Cerrar
              </Button>
            </ModalFooter>
          </>
        )}
      </ModalContent>
    </Modal>
  );
}
