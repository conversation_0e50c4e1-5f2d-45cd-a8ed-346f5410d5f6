import React, { useMemo, useState } from "react";
import {
  Input,
  Table,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow,
  Pagination,
  Chip,
  Spinner,
  Card,
  CardBody,
} from "@heroui/react";
import { Icon } from "@iconify/react";
import { useQuery } from "@apollo/client";

import { FilterDropdown } from "@/components/projects/projects-table/filter-dropdown";
import { AllFieldsDocument, FieldType } from "@/graphql/schemas/generated";

interface SelectableFieldsTableProps {
  selectedFields: string[];
  onSelectionChange: (selectedFields: string[]) => void;
}

export default function SelectableFieldsTable({
  selectedFields,
  onSelectionChange,
}: SelectableFieldsTableProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [page, setPage] = useState(1);
  const [activeFilters, setActiveFilters] = useState<Record<string, string[]>>(
    {},
  );
  const [sortConfig, setSortConfig] = useState<{
    column: string;
    direction: "asc" | "desc";
  } | null>(null);

  const rowsPerPage = 10;

  // Helper function to convert weight enum to decimal
  const formatWeight = (weight: any): string => {
    if (!weight) return "0";

    const weightStr = String(weight);
    // Extract the numeric part after the last underscore
    const match = weightStr.match(/_(\d+(?:_\d+)?)$/);

    if (match) {
      // Replace underscores with dots in the extracted number part
      const numericPart = match[1].replace("_", ".");

      return numericPart;
    } else if (!isNaN(parseFloat(weightStr))) {
      // If it's already a number or can be parsed as one
      return weightStr;
    }

    return "0";
  };

  const { data, loading, error } = useQuery(AllFieldsDocument);

  const fields = data?.allFields || [];

  const filteredAndSortedFields = useMemo(() => {
    let filtered = fields.filter((field: FieldType) => {
      if (!field) return false;

      // Search filter
      const searchMatch =
        field.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        field.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        field.type?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        field.subphase?.name
          ?.toLowerCase()
          .includes(searchTerm.toLowerCase()) ||
        field.subphase?.phase?.name
          ?.toLowerCase()
          .includes(searchTerm.toLowerCase());

      if (!searchMatch) return false;

      // Column filters
      for (const [column, values] of Object.entries(activeFilters)) {
        if (values.length === 0) continue;

        let fieldValue = "";

        switch (column) {
          case "name":
            fieldValue = field.name || "";
            break;
          case "type":
            fieldValue = field.type || "";
            break;
          case "phase":
            fieldValue = field.subphase?.phase?.name || "";
            break;
          case "subphase":
            fieldValue = field.subphase?.name || "";
            break;
          case "isMilestone":
            fieldValue = field.isMilestone ? "Sí" : "No";
            break;
          case "weight":
            fieldValue = formatWeight(field.weight);
            break;
          default:
            fieldValue = "";
        }

        if (!values.includes(fieldValue)) return false;
      }

      return true;
    });

    // Sort
    if (sortConfig) {
      filtered.sort((a: FieldType, b: FieldType) => {
        let aValue = "";
        let bValue = "";

        switch (sortConfig.column) {
          case "name":
            aValue = a.name || "";
            bValue = b.name || "";
            break;
          case "type":
            aValue = a.type || "";
            bValue = b.type || "";
            break;
          case "phase":
            aValue = a.subphase?.phase?.name || "";
            bValue = b.subphase?.phase?.name || "";
            break;
          case "subphase":
            aValue = a.subphase?.name || "";
            bValue = b.subphase?.name || "";
            break;
          case "weight":
            return sortConfig.direction === "asc"
              ? (Number(formatWeight(a.weight)) || 0) -
                  (Number(formatWeight(b.weight)) || 0)
              : (Number(formatWeight(b.weight)) || 0) -
                  (Number(formatWeight(a.weight)) || 0);
          default:
            return 0;
        }

        const comparison = aValue.localeCompare(bValue);

        return sortConfig.direction === "asc" ? comparison : -comparison;
      });
    }

    return filtered;
  }, [fields, searchTerm, activeFilters, sortConfig]);

  const pages = Math.ceil(filteredAndSortedFields.length / rowsPerPage);
  const items = useMemo(() => {
    const start = (page - 1) * rowsPerPage;
    const end = start + rowsPerPage;

    return filteredAndSortedFields.slice(start, end);
  }, [page, filteredAndSortedFields]);

  const handleSelectionChange = (keys: any) => {
    if (keys === "all") {
      const allKeys = filteredAndSortedFields.map(
        (field: FieldType) => field.id,
      );

      onSelectionChange(allKeys);
    } else {
      const newSelection = Array.from(keys).map((key) => String(key));

      onSelectionChange(newSelection);
    }
  };

  const handleFilterChange = (column: string, values: string[]) => {
    setActiveFilters((prev) => ({ ...prev, [column]: values }));
    setPage(1);
  };

  const handleSort = (column: string, direction: "asc" | "desc") => {
    setSortConfig({ column, direction });
  };

  const getUniqueValues = (column: string): string[] => {
    return Array.from(
      new Set(
        fields
          .map((field: FieldType) => {
            switch (column) {
              case "name":
                return field?.name || "";
              case "type":
                return field?.type || "";
              case "phase":
                return field?.subphase?.phase?.name || "";
              case "subphase":
                return field?.subphase?.name || "";
              case "isMilestone":
                return field?.isMilestone ? "Sí" : "No";
              case "weight":
                return formatWeight(field?.weight);
              default:
                return "";
            }
          })
          .filter(Boolean) as string[],
      ),
    );
  };

  if (loading) {
    return (
      <Card>
        <CardBody className="flex items-center justify-center py-10">
          <Spinner label="Cargando campos..." />
        </CardBody>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardBody className="flex items-center justify-center py-10">
          <p className="text-danger">
            Error al cargar los campos: {error.message}
          </p>
        </CardBody>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Search */}
      <Input
        isClearable
        placeholder="Buscar campos..."
        startContent={<Icon icon="heroicons:magnifying-glass" />}
        value={searchTerm}
        onClear={() => setSearchTerm("")}
        onValueChange={setSearchTerm}
      />

      {/* Selected count */}
      {selectedFields.length > 0 && (
        <Card className="bg-primary-50 dark:bg-primary-900">
          <CardBody className="py-2">
            <p className="text-primary-600 dark:text-primary-300 text-sm">
              {selectedFields.length} campo(s) seleccionado(s)
            </p>
          </CardBody>
        </Card>
      )}

      {/* Table */}
      <Table
        removeWrapper
        aria-label="Tabla de campos seleccionables"
        bottomContent={
          pages > 1 ? (
            <div className="flex w-full justify-center">
              <Pagination
                isCompact
                showControls
                showShadow
                color="primary"
                page={page}
                total={pages}
                onChange={setPage}
              />
            </div>
          ) : null
        }
        selectedKeys={new Set(selectedFields)}
        selectionMode="multiple"
        onSelectionChange={handleSelectionChange}
      >
        <TableHeader>
          <TableColumn>
            <FilterDropdown
              activeFilters={activeFilters}
              column="name"
              items={getUniqueValues("name")}
              sortConfig={sortConfig}
              title="Nombre"
              onFilter={handleFilterChange}
              onSort={handleSort}
            />
          </TableColumn>
          <TableColumn>Descripción</TableColumn>
          <TableColumn>
            <FilterDropdown
              activeFilters={activeFilters}
              column="type"
              displayText={{
                informative: "Informativo",
                selection: "Selección",
                task_with_subtasks: "Subtarea",
                task: "Tarea",
                document: "Documento",
              }}
              items={getUniqueValues("type")}
              sortConfig={sortConfig}
              title="Tipo"
              onFilter={handleFilterChange}
              onSort={handleSort}
            />
          </TableColumn>
          <TableColumn>
            <FilterDropdown
              activeFilters={activeFilters}
              column="phase"
              items={getUniqueValues("phase")}
              sortConfig={sortConfig}
              title="Fase"
              onFilter={handleFilterChange}
              onSort={handleSort}
            />
          </TableColumn>
          <TableColumn>
            <FilterDropdown
              activeFilters={activeFilters}
              column="subphase"
              items={getUniqueValues("subphase")}
              sortConfig={sortConfig}
              title="Subfase"
              onFilter={handleFilterChange}
              onSort={handleSort}
            />
          </TableColumn>
          <TableColumn>
            <FilterDropdown
              activeFilters={activeFilters}
              column="isMilestone"
              items={getUniqueValues("isMilestone")}
              sortConfig={sortConfig}
              title="Hito"
              onFilter={handleFilterChange}
              onSort={handleSort}
            />
          </TableColumn>
          <TableColumn>
            <FilterDropdown
              activeFilters={activeFilters}
              column="weight"
              items={getUniqueValues("weight")}
              sortConfig={sortConfig}
              title="Peso"
              onFilter={handleFilterChange}
              onSort={handleSort}
            />
          </TableColumn>
        </TableHeader>
        <TableBody emptyContent="No hay campos disponibles" items={items}>
          {(item: FieldType) => (
            <TableRow key={item.id}>
              <TableCell>{item.name}</TableCell>
              <TableCell>
                <div className="max-w-xs">
                  <p className="text-sm text-default-600 line-clamp-2">
                    {item.description || "Sin descripción"}
                  </p>
                </div>
              </TableCell>
              <TableCell>
                <Chip size="sm" variant="flat">
                  {item.type === "informative"
                    ? "Informativo"
                    : item.type === "selection"
                      ? "Selección"
                      : item.type === "task_with_subtasks"
                        ? "Subtarea"
                        : item.type === "task"
                          ? "Tarea"
                          : item.type === "document"
                            ? "Documento"
                            : item.type}
                </Chip>
              </TableCell>
              <TableCell>{typeof item.subphase?.phase?.name === "string" ? item.subphase?.phase?.name.toLowerCase() === "incubadora" ? "TAKE OFF" : item.subphase?.phase?.name : "Sin fase"}</TableCell>
              <TableCell>{item.subphase?.name}</TableCell>
              <TableCell>
                {item.isMilestone ? (
                  <Chip color="success" size="sm" variant="flat">
                    Sí
                  </Chip>
                ) : (
                  <Chip color="danger" size="sm" variant="flat">
                    No
                  </Chip>
                )}
              </TableCell>
              <TableCell>{formatWeight(item.weight)}</TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  );
}
