"use client";

import React from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
} from "@heroui/react";
import { Icon } from "@iconify/react";

import { useAllFieldsQuery } from "@/graphql/schemas/generated";

interface ReportSummaryProps {
  selectedFields: string[];
  fieldFilters: Record<string, "all" | "with_content" | "without_content">;
  onCreateReport: () => void;
}

const getFieldTypeDisplayName = (fieldType: string) => {
  switch (fieldType) {
    case "informative":
      return "Informativo";
    case "selection":
      return "Selección";
    case "task":
      return "Tarea";
    case "document":
      return "Documento";
    case "task_with_subtasks":
      return "Subtarea";
    default:
      return fieldType;
  }
};

const getFilterDisplayName = (
  filterOption: string,
  fieldType: string,
): string => {
  if (filterOption === "all") return "Todos";

  switch (fieldType) {
    case "informative":
      return filterOption === "with_content"
        ? "Con contenido"
        : "Sin contenido";
    case "selection":
      return filterOption === "with_content"
        ? "Con selección"
        : "Sin selección";
    case "task":
    case "task_with_subtasks":
      return filterOption === "with_content" ? "Completadas" : "Pendientes";
    case "document":
      return filterOption === "with_content"
        ? "Con documento"
        : "Sin documento";
    default:
      return filterOption === "with_content"
        ? "Con contenido"
        : "Sin contenido";
  }
};

const getFieldTypeIcon = (fieldType: string) => {
  switch (fieldType) {
    case "informative":
      return "heroicons:information-circle";
    case "selection":
      return "heroicons:list-bullet";
    case "task":
      return "heroicons:check-circle";
    case "document":
      return "heroicons:document";
    case "task_with_subtasks":
      return "heroicons:squares-plus";
    default:
      return "heroicons:question-mark-circle";
  }
};

export default function ReportSummary({
  selectedFields,
  fieldFilters,
  onCreateReport,
}: ReportSummaryProps) {
  const { data: fieldsData } = useAllFieldsQuery();

  const fields = fieldsData?.allFields || [];

  // Get selected field details
  const selectedFieldsData = fields.filter((field) =>
    selectedFields.includes(field?.id || ""),
  );

  // Group fields by type for summary
  const fieldsByType = selectedFieldsData.reduce(
    (acc, field) => {
      const type = field?.type || "unknown";

      if (!acc[type]) {
        acc[type] = [];
      }
      acc[type].push(field);

      return acc;
    },
    {} as Record<string, any[]>,
  );

  // Get filter count
  const filterCount = Object.keys(fieldFilters).length;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h3 className="text-xl font-semibold mb-2">Resumen del reporte</h3>
        <p className="text-default-500">
          Revisa la configuración antes de crear el reporte
        </p>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Fields Summary */}
        <Card>
          <CardHeader className="pb-2">
            <div className="flex items-center gap-2">
              <Icon
                className="text-primary"
                icon="heroicons:squares-2x2"
                width={20}
              />
              <h4 className="text-lg font-semibold">Campos seleccionados</h4>
            </div>
          </CardHeader>
          <CardBody className="pt-0">
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm text-default-600">
                  Total de campos:
                </span>
                <Chip color="primary" size="sm" variant="flat">
                  {selectedFields.length}
                </Chip>
              </div>
              <Divider />
              <div className="space-y-2">
                {Object.entries(fieldsByType).map(([type, fieldsOfType]) => (
                  <div key={type} className="flex justify-between items-center">
                    <div className="flex items-center gap-2">
                      <Icon
                        className="text-default-500"
                        icon={getFieldTypeIcon(type)}
                        width={16}
                      />
                      <span className="text-sm">
                        {getFieldTypeDisplayName(type)}
                      </span>
                    </div>
                    <span className="text-sm font-medium">
                      {fieldsOfType.length}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </CardBody>
        </Card>

        {/* Filters Summary */}
        <Card>
          <CardHeader className="pb-2">
            <div className="flex items-center gap-2">
              <Icon
                className="text-primary"
                icon="heroicons:funnel"
                width={20}
              />
              <h4 className="text-lg font-semibold">Filtros configurados</h4>
            </div>
          </CardHeader>
          <CardBody className="pt-0">
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm text-default-600">
                  Total de filtros:
                </span>
                <Chip color="secondary" size="sm" variant="flat">
                  {filterCount}
                </Chip>
              </div>
              <Divider />
              <div className="space-y-2">
                {Object.entries(fieldsByType).map(([type, fieldsOfType]) => {
                  const filterOption = fieldFilters[type] || "all";

                  return (
                    <div key={type} className="space-y-1">
                      <div className="flex items-center gap-2">
                        <Icon
                          className="text-default-500"
                          icon={getFieldTypeIcon(type)}
                          width={16}
                        />
                        <span className="text-sm font-medium">
                          {getFieldTypeDisplayName(type)}
                        </span>
                      </div>
                      <div className="ml-6 space-y-1 text-xs text-default-600">
                        <div>
                          • {getFilterDisplayName(filterOption, type)} (
                          {fieldsOfType.length} campo(s))
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </CardBody>
        </Card>
      </div>

      {/* Detailed Fields List */}
      <Card>
        <CardHeader className="pb-2">
          <div className="flex items-center gap-2">
            <Icon
              className="text-primary"
              icon="heroicons:list-bullet"
              width={20}
            />
            <h4 className="text-lg font-semibold">
              Detalle de campos y filtros
            </h4>
          </div>
        </CardHeader>
        <CardBody className="pt-0">
          <div className="space-y-3">
            {selectedFieldsData.map((field) => {
              const fieldType = field?.type || "";
              const filterOption = fieldFilters[fieldType] || "all";

              return (
                <div
                  key={field?.id}
                  className="flex items-center justify-between p-3 bg-default-50 rounded-lg"
                >
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <Icon
                        className="text-default-500"
                        icon={getFieldTypeIcon(fieldType)}
                        width={16}
                      />
                      <span className="font-medium">{field?.name}</span>
                      <Chip size="sm" variant="flat">
                        {getFieldTypeDisplayName(fieldType)}
                      </Chip>
                    </div>
                    <div className="text-sm text-default-600">
                      {field?.subphase?.phase?.name} - {field?.subphase?.name}
                    </div>
                  </div>
                  <div className="ml-4">
                    <Chip
                      color={
                        filterOption === "all"
                          ? "primary"
                          : filterOption === "with_content"
                            ? "success"
                            : "warning"
                      }
                      size="sm"
                      variant="flat"
                    >
                      {getFilterDisplayName(filterOption, fieldType)}
                    </Chip>
                  </div>
                </div>
              );
            })}
          </div>
        </CardBody>
      </Card>

      {/* Create Button */}
      <div className="flex justify-center pt-4">
        <Button
          color="primary"
          size="lg"
          startContent={<Icon icon="heroicons:document-plus" width={20} />}
          onPress={onCreateReport}
        >
          Crear Reporte
        </Button>
      </div>
    </div>
  );
}
