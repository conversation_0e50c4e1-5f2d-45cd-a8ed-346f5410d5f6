import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Tab,
  Input,
  Textarea,
  Spinner,
  Card,
  CardBody,
  CardHeader,
} from "@heroui/react";
import { Icon } from "@iconify/react";

import { useTemplateDetail } from "@/hooks/templates/useTemplateDetail";
import { useTemplates } from "@/hooks/templates/useTemplates";
import SelectableRulesTable from "@/components/template/create/selectable-rules-table";
import SelectableNotificationsTable from "@/components/template/create/selectable-notifications-table";

interface Template {
  id: number;
  title: string;
  description: string;
  is_active: boolean;
}

interface TemplateEditModalProps {
  isOpen: boolean;
  template: Template | null;
  onClose: () => void;
  onSave?: () => void;
}

export function TemplateEditModal({
  isOpen,
  template,
  onClose,
  onSave,
}: TemplateEditModalProps) {
  const {
    templateDeta<PERSON>,
    loading,
    error,
    fetchTemplateDetail,
    clearTemplateDetail,
  } = useTemplateDetail();
  const { updateTemplate } = useTemplates();
  const [selectedRules, setSelectedRules] = useState<string[]>([]);
  const [selectedNotifications, setSelectedNotifications] = useState<string[]>(
    [],
  );
  const [templateData, setTemplateData] = useState({
    name: "",
    description: "",
  });
  const [saving, setSaving] = useState(false);

  // Fetch template details when modal opens
  useEffect(() => {
    if (isOpen && template) {
      fetchTemplateDetail(template.id.toString());
      setTemplateData({
        name: template.title,
        description: template.description,
      });
    } else if (!isOpen) {
      clearTemplateDetail();
      setSelectedRules([]);
      setSelectedNotifications([]);
    }
  }, [isOpen, template]);

  // Set selected items when template detail is loaded
  useEffect(() => {
    if (templateDetail?.full_definition) {
      const ruleIds = templateDetail.full_definition.rules.map((rule) =>
        rule.id.toString(),
      );
      const notificationIds = templateDetail.full_definition.notifications.map(
        (notification) => notification.id.toString(),
      );

      setSelectedRules(ruleIds);
      setSelectedNotifications(notificationIds);
    }
  }, [templateDetail]);

  const handleSave = async () => {
    if (!template) return;

    try {
      setSaving(true);

      const result = await updateTemplate(template.id, {
        selectedRules: selectedRules.map(id => Number(id)),
        selectedNotifications: selectedNotifications.map(id => Number(id)),
      });

      if (result.success) {
        // Call the onSave callback if provided (for parent component to refresh data)
        if (onSave) {
          onSave();
        }
        onClose();
      } else {
        // Handle error - could show a toast notification here
        console.error("Failed to update template:", result.error);
      }
    } catch (error) {
      console.error("Error updating template:", error);
    } finally {
      setSaving(false);
    }
  };

  const handleClose = () => {
    onClose();
  };

  if (!template) return null;

  return (
    <Modal
      backdrop="opaque"
      isOpen={isOpen}
      scrollBehavior="inside"
      size="5xl"
      onClose={handleClose}
    >
      <ModalContent>
        <ModalHeader>
          <div className="flex items-center gap-2">
            <Icon className="text-xl" icon="lucide:edit-3" />
            <h3 className="text-xl font-bold">Editar Plantilla</h3>
          </div>
        </ModalHeader>
        <ModalBody>
          {loading ? (
            <div className="flex justify-center items-center min-h-[400px]">
              <Spinner size="lg" />
            </div>
          ) : error ? (
            <div className="flex justify-center items-center min-h-[400px]">
              <div className="text-center">
                <Icon
                  className="text-4xl text-danger mb-2"
                  icon="lucide:alert-circle"
                />
                <p className="text-danger">Error loading template: {error}</p>
              </div>
            </div>
          ) : (
            <div className="space-y-6">
              {/* Template Basic Info */}
              <Card>
                <CardHeader>
                  <h4 className="text-lg font-semibold">
                    Información de la Plantilla
                  </h4>
                </CardHeader>
                <CardBody className="space-y-4">
                  <Input
                    isReadOnly
                    label="Nombre"
                    value={templateData.name}
                    variant="bordered"
                    onValueChange={(value) =>
                      setTemplateData({ ...templateData, name: value })
                    }
                  />
                  <Textarea
                    isReadOnly
                    label="Descripción"
                    minRows={2}
                    value={templateData.description}
                    variant="bordered"
                    onValueChange={(value) =>
                      setTemplateData({ ...templateData, description: value })
                    }
                  />
                </CardBody>
              </Card>

              {/* Tabs for Rules and Notifications */}
              <Tabs aria-label="Template edit options" className="w-full">
                <Tab
                  key="rules"
                  title={
                    <div className="flex items-center space-x-2">
                      <Icon icon="lucide:settings" />
                      <span>Reglas ({selectedRules.length})</span>
                    </div>
                  }
                >
                  <div className="mt-4">
                    <SelectableRulesTable
                      selectedFields={
                        templateDetail?.full_definition?.fields?.map((f) =>
                          f.id.toString(),
                        ) || []
                      }
                      selectedRules={selectedRules}
                      onSelectionChange={setSelectedRules}
                    />
                  </div>
                </Tab>
                <Tab
                  key="notifications"
                  title={
                    <div className="flex items-center space-x-2">
                      <Icon icon="lucide:bell" />
                      <span>
                        Notificaciones ({selectedNotifications.length})
                      </span>
                    </div>
                  }
                >
                  <div className="">
                    <SelectableNotificationsTable
                      selectedFields={
                        templateDetail?.full_definition?.fields?.map((f) =>
                          f.id.toString(),
                        ) || []
                      }
                      selectedNotifications={selectedNotifications}
                      onSelectionChange={setSelectedNotifications}
                    />
                  </div>
                </Tab>
              </Tabs>
            </div>
          )}
        </ModalBody>
        <ModalFooter>
          <Button variant="flat" onPress={handleClose}>
            Cancelar
          </Button>
          <Button
            color="primary"
            isDisabled={loading || saving}
            isLoading={saving}
            onPress={handleSave}
          >
            {saving ? "Guardando..." : "Guardar Cambios"}
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
}
