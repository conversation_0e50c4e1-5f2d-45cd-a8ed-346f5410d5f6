"use client";
import { useState, useEffect } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Input,
  Select,
  SelectItem,
  Form,
  Autocomplete,
  AutocompleteItem,
} from "@heroui/react";
import { useQuery } from "@apollo/client";

import { GET_ALL_BASIC_PROJECTS_INFO } from "@/graphql/operations/projects";

interface Contact {
  id: number;
  name: string;
  email: string;
  type: string;
  position: string;
  project: number;
  created_at: string;
}

interface EditContactFormProps {
  onClose: () => void;
  onUpdateContact: (contact: Contact) => void;
  contact: Contact;
}

const contactTypes = [
  "Empresa",
  "Agregador",
  "Add-on",
  "OOS",
  "Impuestos",
  "SS",
  "GL File",
];

const EditContactForm = ({
  onClose,
  onUpdateContact,
  contact,
}: EditContactFormProps) => {
  const { data: allProjects } = useQuery(GET_ALL_BASIC_PROJECTS_INFO);

  const [formData, setFormData] = useState({
    id: contact.id,
    type: contact.type,
    name: contact.name,
    email: contact.email,
    position: contact.position,
    project: contact.project,
  });

  const [selectedProjectKey, setSelectedProjectKey] = useState<string | null>(
    null,
  );

  // Initialize form data when contact changes
  useEffect(() => {
    setFormData({
      id: contact.id,
      type: contact.type,
      name: contact.name,
      email: contact.email,
      position: contact.position,
      project: contact.project,
    });

    // Set the selected project key based on the project ID
    if (contact.project) {
      setSelectedProjectKey(contact.project.toString());
    }
  }, [contact]);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>,
  ) => {
    const { name, value } = e.target;

    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleProjectChange = (key: string | null) => {
    setSelectedProjectKey(key);
    const projectId = key ? Number(key) : 0;

    setFormData((prev) => ({
      ...prev,
      project: projectId,
    }));
  };

  const handleSubmit = () => {
    onUpdateContact({
      ...formData,
      created_at: contact.created_at,
    });
  };

  return (
    <>
      <ModalHeader>Editar Contacto</ModalHeader>
      <Form
        onSubmit={(e) => {
          e.preventDefault();
          handleSubmit();
        }}
      >
        <ModalBody className="w-full">
          <div className="flex flex-col gap-4 w-full">
            <div className="w-full">
              <label
                className="block text-sm font-medium mb-1"
                htmlFor="contact-type"
              >
                Tipo de contacto
              </label>
              <Select
                className="w-full"
                id="contact-type"
                name="type"
                placeholder="Selecciona un tipo"
                selectedKeys={[formData.type]}
                selectionMode="single"
                onChange={handleChange}
              >
                {contactTypes.map((type) => (
                  <SelectItem key={type}>{type}</SelectItem>
                ))}
              </Select>
            </div>
            <div>
              <label
                className="block text-sm font-medium mb-1"
                htmlFor="contact-name"
              >
                Nombre
              </label>
              <Input
                className="w-full"
                id="contact-name"
                name="name"
                placeholder="Nombre completo"
                value={formData.name}
                onChange={handleChange}
              />
            </div>
            <div>
              <label
                className="block text-sm font-medium mb-1"
                htmlFor="contact-email"
              >
                Correo electrónico
              </label>
              <Input
                className="w-full"
                id="contact-email"
                name="email"
                placeholder="<EMAIL>"
                type="email"
                value={formData.email}
                onChange={handleChange}
              />
            </div>
            <div>
              <label
                className="block text-sm font-medium mb-1"
                htmlFor="contact-position"
              >
                Cargo
              </label>
              <Input
                className="w-full"
                id="contact-position"
                name="position"
                placeholder="Cargo o posición"
                value={formData.position}
                onChange={handleChange}
              />
            </div>
            <div>
              <label
                className="block text-sm font-medium mb-1"
                htmlFor="contact-project"
              >
                Implementación
              </label>
              <Autocomplete
                className="w-full"
                id="contact-project"
                placeholder="Buscar implementación..."
                selectedKey={selectedProjectKey}
                onSelectionChange={(key) => {
                  handleProjectChange(key as string);
                }}
              >
                {allProjects?.allProjects?.map((project: any) => (
                  <AutocompleteItem
                    key={project.id.toString()}
                    textValue={`${project.lid} - ${project.companyName}`}
                  >
                    <div className="text-default-700">
                      <div className="font-medium">
                        {project.lid} - {project.companyName}
                      </div>
                      <div className="text-sm text-default-500">
                        {project.aggregator}
                      </div>
                      <div className="text-xs text-default-400">
                        Alias: {project.alias}
                      </div>
                    </div>
                  </AutocompleteItem>
                ))}
              </Autocomplete>
            </div>
          </div>
        </ModalBody>
        <ModalFooter className="justify-end w-full">
          <Button
            color="danger"
            type="button"
            variant="light"
            onPress={onClose}
          >
            Cancelar
          </Button>
          <Button color="primary" type="submit">
            Actualizar
          </Button>
        </ModalFooter>
      </Form>
    </>
  );
};

export default EditContactForm;
