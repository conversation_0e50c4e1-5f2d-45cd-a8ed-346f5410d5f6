"use client";

import { useState, useEffect, useCallback } from "react";
import {
  Button,
  Input,
  Card,
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  Modal,
  ModalContent,
  <PERSON><PERSON><PERSON>,
  Spinner,
} from "@heroui/react";
import { Icon } from "@iconify/react";
import { useQuery } from "@apollo/client";

import { title } from "@/components/primitives";
import { useAuth } from "@/hooks/auth/useAuth";
import CreateContactForm from "@/components/contacts/create-contact-form";
import EditContactForm from "@/components/contacts/edit-contact-form";
import DeleteContactModal from "@/components/contacts/delete-contact-modal";
import { FilterDropdown } from "@/components/projects/projects-table/filter-dropdown";
import { useRowCountStore } from "@/store/use-row-count-store";
import { useContacts, Contact } from "@/hooks/contacts/useContacts";
import { GET_ALL_BASIC_PROJECTS_INFO } from "@/graphql/operations/projects";

export default function ContactosPage() {
  const { rowCount } = useRowCountStore();

  // Use the contacts hook for API operations
  const {
    contacts,
    loading: contactsLoading,
    error: contactsError,
    fetchAllContacts,
    createContact,
    updateContact,
    deleteContact,
  } = useContacts();

  // Fetch projects data for LID - Company Name display
  const { data: allProjects, loading: projectsLoading } = useQuery(
    GET_ALL_BASIC_PROJECTS_INFO,
  );

  const [filteredContacts, setFilteredContacts] = useState<Contact[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedContact, setSelectedContact] = useState<Contact | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [page, setPage] = useState(1);

  // Filter and sort states
  const [activeFilters, setActiveFilters] = useState<Record<string, string[]>>(
    {},
  );
  const [sortConfig, setSortConfig] = useState<{
    column: string;
    direction: "asc" | "desc";
  } | null>(null);

  // Check if user has permission to edit configuration
  const { hasPermission } = useAuth();
  const [canEditConfiguration, setCanEditConfiguration] = useState(false);

  // Fetch contacts on component mount
  useEffect(() => {
    fetchAllContacts();
  }, []);

  // Update filtered contacts when contacts change
  useEffect(() => {
    setFilteredContacts(contacts);
  }, [contacts]);

  useEffect(() => {
    setCanEditConfiguration(hasPermission("editar_config"));
  }, [hasPermission]);

  // Helper function to get project display format (LID - Company Name)
  const getProjectDisplay = useCallback(
    (projectId: number): string => {
      if (!allProjects?.allProjects) return "Cargando...";

      const project = allProjects.allProjects.find(
        (p: any) => p.id === projectId.toString(),
      );

      if (!project) return "Sin proyecto";

      return `${project.lid} - ${project.companyName}`;
    },
    [allProjects, projectsLoading],
  );

  // Calculate pagination
  const pages = Math.ceil(filteredContacts.length / rowCount);
  const items = filteredContacts.slice((page - 1) * rowCount, page * rowCount);

  // Function to get unique values for filter dropdown
  const getUniqueValues = (column: keyof Contact): string[] => {
    if (column === "project") {
      // For project column, return unique project display strings
      const uniqueProjectIds = Array.from(
        new Set(contacts.map((contact) => contact.project)),
      );

      return uniqueProjectIds.map((id) => getProjectDisplay(id));
    }

    const uniqueValues = Array.from(
      new Set(
        contacts.map((contact) => {
          const value = contact[column];

          return typeof value === "string" ? value : String(value);
        }),
      ),
    );

    return uniqueValues;
  };

  // Apply filters and search
  const applyFiltersAndSort = (
    searchValue: string,
    filters: Record<string, string[]>,
    sort: { column: string; direction: "asc" | "desc" } | null,
  ) => {
    let filtered = [...contacts];

    // Apply search
    if (searchValue) {
      filtered = filtered.filter(
        (contact) =>
          contact.name.toLowerCase().includes(searchValue.toLowerCase()) ||
          contact.email.toLowerCase().includes(searchValue.toLowerCase()) ||
          contact.type.toLowerCase().includes(searchValue.toLowerCase()) ||
          contact.position.toLowerCase().includes(searchValue.toLowerCase()),
      );
    }

    // Apply filters
    Object.entries(filters).forEach(([column, values]) => {
      if (values.length > 0) {
        filtered = filtered.filter((contact) =>
          values.includes(String(contact[column as keyof Contact])),
        );
      }
    });

    // Apply sorting
    if (sort) {
      filtered.sort((a, b) => {
        const aValue = String(a[sort.column as keyof Contact]);
        const bValue = String(b[sort.column as keyof Contact]);

        if (sort.direction === "asc") {
          return aValue.localeCompare(bValue);
        } else {
          return bValue.localeCompare(aValue);
        }
      });
    }

    setFilteredContacts(filtered);
    setPage(1); // Reset to first page when filtering
  };

  // Handle filter changes
  const handleFilterChange = (column: string, values: string[]): void => {
    const newFilters = { ...activeFilters };

    if (values.length === 0) {
      delete newFilters[column];
    } else {
      newFilters[column] = values;
    }

    setActiveFilters(newFilters);
    applyFiltersAndSort(searchTerm, newFilters, sortConfig);
  };

  // Handle sorting
  const handleSort = (column: string, direction: "asc" | "desc") => {
    const newSortConfig = { column, direction };

    setSortConfig(newSortConfig);
    applyFiltersAndSort(searchTerm, activeFilters, newSortConfig);
  };

  // Handle search
  const handleSearch = (value: string) => {
    setSearchTerm(value);
    applyFiltersAndSort(value, activeFilters, sortConfig);
  };

  // Handle clear filters
  const handleClearFilters = () => {
    setSearchTerm("");
    setActiveFilters({});
    setSortConfig(null);
    setFilteredContacts(contacts);
    setPage(1);
  };

  // Handle add contact
  const handleAddContact = async (
    newContact: Omit<Contact, "id" | "created_at">,
  ) => {
    setIsLoading(true);
    try {
      await createContact(newContact);
      applyFiltersAndSort(searchTerm, activeFilters, sortConfig);
      setIsCreateModalOpen(false);
    } catch (error) {
      console.error("Error creating contact:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle edit contact
  const handleEditContact = (contact: Contact) => {
    setSelectedContact(contact);
    setIsEditModalOpen(true);
  };

  // Handle update contact
  const handleUpdateContact = async (updatedContact: Contact) => {
    setIsLoading(true);
    try {
      await updateContact(updatedContact.id, {
        name: updatedContact.name,
        email: updatedContact.email,
        type: updatedContact.type,
        position: updatedContact.position,
        project: updatedContact.project,
      });
      applyFiltersAndSort(searchTerm, activeFilters, sortConfig);
      setIsEditModalOpen(false);
      setSelectedContact(null);
    } catch (error) {
      console.error("Error updating contact:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle delete contact
  const handleDeleteContact = (contact: Contact) => {
    setSelectedContact(contact);
    setIsDeleteModalOpen(true);
  };

  // Handle confirm delete
  const handleConfirmDelete = async () => {
    if (!selectedContact) return;

    setIsLoading(true);
    try {
      await deleteContact(selectedContact.id);
      applyFiltersAndSort(searchTerm, activeFilters, sortConfig);
      setIsDeleteModalOpen(false);
      setSelectedContact(null);
    } catch (error) {
      console.error("Error deleting contact:", error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      {/* Debug info */}
      {/* <div className="mb-4 p-2 bg-gray-100 text-xs">
        <p>Edit Modal Open: {isEditModalOpen.toString()}</p>
        <p>Delete Modal Open: {isDeleteModalOpen.toString()}</p>
        <p>Selected Contact: {selectedContact ? selectedContact.name : 'None'}</p>
        <p>Can Edit Configuration: {canEditConfiguration.toString()}</p>
      </div> */}

      <div className="flex justify-between items-center w-full mb-6">
        <h2 className={title({ size: "sm" })}>Contactos</h2>
        <Button
          color="primary"
          disabled={!canEditConfiguration}
          startContent={<Icon icon="heroicons:plus" />}
          onPress={() => setIsCreateModalOpen(true)}
        >
          Crear Contacto
        </Button>
      </div>

      {/* Search and filters */}
      <Card className="p-2 mb-4 w-full pt-4" radius="sm">
        <div className="flex flex-col sm:flex-row gap-4 justify-between items-center mb-4">
          <div className="flex gap-2 w-full sm:w-auto">
            <Input
              className="w-full sm:w-96"
              placeholder="Buscar por nombre, email, tipo o cargo..."
              startContent="🔍"
              value={searchTerm}
              onValueChange={handleSearch}
            />
          </div>
          <div className="flex gap-2 w-full sm:w-auto justify-end">
            <Button
              color={
                searchTerm || Object.keys(activeFilters).length > 0
                  ? "primary"
                  : "default"
              }
              variant="flat"
              onPress={handleClearFilters}
            >
              Borrar filtros{" "}
              {(searchTerm || Object.keys(activeFilters).length > 0) &&
                `(${(searchTerm ? 1 : 0) + Object.keys(activeFilters).length})`}
            </Button>
          </div>
        </div>
      </Card>

      {/* Contacts Table */}
      <Table
        key={projectsLoading ? "loading" : "contacts-table"}
        removeWrapper
        aria-label="Contacts table"
        bottomContent={
          pages > 1 ? (
            <div className="flex w-full justify-center">
              <Pagination
                isCompact
                showControls
                showShadow
                color="primary"
                page={page}
                total={pages}
                onChange={(page) => setPage(page)}
              />
            </div>
          ) : null
        }
      >
        <TableHeader>
          <TableColumn>
            <FilterDropdown
              activeFilters={activeFilters}
              column="project"
              items={getUniqueValues("project")}
              sortConfig={sortConfig}
              title="LID - Razón Social"
              onFilter={handleFilterChange}
              onSort={handleSort}
            />
          </TableColumn>
          <TableColumn>
            <FilterDropdown
              activeFilters={activeFilters}
              column="type"
              items={getUniqueValues("type")}
              sortConfig={sortConfig}
              title="Tipo de contacto"
              onFilter={handleFilterChange}
              onSort={handleSort}
            />
          </TableColumn>
          <TableColumn>
            <FilterDropdown
              activeFilters={activeFilters}
              column="position"
              items={getUniqueValues("position")}
              sortConfig={sortConfig}
              title="Cargo"
              onFilter={handleFilterChange}
              onSort={handleSort}
            />
          </TableColumn>
          <TableColumn>
            <FilterDropdown
              activeFilters={activeFilters}
              column="name"
              items={getUniqueValues("name")}
              sortConfig={sortConfig}
              title="Nombre"
              onFilter={handleFilterChange}
              onSort={handleSort}
            />
          </TableColumn>
          <TableColumn>
            <FilterDropdown
              activeFilters={activeFilters}
              column="email"
              items={getUniqueValues("email")}
              sortConfig={sortConfig}
              title="Correo"
              onFilter={handleFilterChange}
              onSort={handleSort}
            />
          </TableColumn>
          <TableColumn>Acciones</TableColumn>
        </TableHeader>
        <TableBody
          isLoading={contactsLoading || isLoading}
          items={items}
          loadingContent={<Spinner label="Cargando contactos..." />}
        >
          {(contact) => (
            <TableRow key={contact.id}>
              <TableCell>{getProjectDisplay(contact.project)}</TableCell>
              <TableCell>{contact.type}</TableCell>
              <TableCell>{contact.position}</TableCell>
              <TableCell>{contact.name}</TableCell>
              <TableCell className="underline">
                <a href={`mailto:${contact.email}`}>{contact.email}</a>
              </TableCell>
              <TableCell>
                <div className="flex gap-2">
                  <Button
                    isIconOnly
                    color="primary"
                    disabled={!canEditConfiguration}
                    size="sm"
                    variant="flat"
                    onPress={() => handleEditContact(contact)}
                  >
                    <Icon className="text-lg" icon="lucide:edit-3" />
                  </Button>
                  <Button
                    isIconOnly
                    color="danger"
                    disabled={!canEditConfiguration}
                    size="sm"
                    variant="flat"
                    onPress={() => handleDeleteContact(contact)}
                  >
                    <Icon className="text-lg" icon="lucide:trash" />
                  </Button>
                </div>
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>

      {/* Create Contact Modal */}
      <Modal
        isDismissable={false}
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
      >
        <ModalContent>
          {() => (
            <CreateContactForm
              onAddContact={handleAddContact}
              onClose={() => setIsCreateModalOpen(false)}
            />
          )}
        </ModalContent>
      </Modal>

      {/* Edit Contact Modal */}
      <Modal
        isDismissable={false}
        isOpen={isEditModalOpen}
        onClose={() => {
          setIsEditModalOpen(false);
          setSelectedContact(null);
        }}
      >
        <ModalContent>
          {() =>
            selectedContact && (
              <EditContactForm
                contact={selectedContact}
                onClose={() => {
                  setIsEditModalOpen(false);
                  setSelectedContact(null);
                }}
                onUpdateContact={handleUpdateContact}
              />
            )
          }
        </ModalContent>
      </Modal>

      {/* Delete Contact Modal */}
      <Modal
        isOpen={isDeleteModalOpen}
        onClose={() => {
          setIsDeleteModalOpen(false);
          setSelectedContact(null);
        }}
      >
        <ModalContent>
          {() =>
            selectedContact && (
              <DeleteContactModal
                contact={selectedContact}
                isLoading={isLoading}
                onClose={() => {
                  setIsDeleteModalOpen(false);
                  setSelectedContact(null);
                }}
                onConfirm={handleConfirmDelete}
              />
            )
          }
        </ModalContent>
      </Modal>
    </>
  );
}
