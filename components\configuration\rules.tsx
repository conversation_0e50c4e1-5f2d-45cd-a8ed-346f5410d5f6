"use client";

import React, { useState, useEffect } from "react";
import {
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  Button,
  Pagination,
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  Input,
  Chip,
  Select,
  SelectItem,
  Card,
  Spinner,
} from "@heroui/react";
import {
  Autocomplete,
  AutocompleteItem,
  AutocompleteSection,
} from "@heroui/autocomplete";
import { Icon } from "@iconify/react";
import { useQuery, useMutation } from "@apollo/client";

import { FilterDropdown } from "../projects/projects-table/filter-dropdown";

import {
  AllRulesDocument,
  AllFieldsDocument,
  ExecuteCreateRuleDocument,
  ExecuteUpdateRuleDocument,
  FieldType,
  RulesRuleActionChoices,
  RulesRuleConditionChoices,
  RulesRuleStatusChoices,
} from "@/graphql/schemas/generated";
import { useRowCountStore } from "@/store/use-row-count-store";
import { phaseColors } from "@/components/primitives";

// Component for the Add Subphase Modal
interface RulesModalProps {
  isCreating: boolean;
  setIsCreating: (isCreating: boolean) => void;
  canEditConfiguration: boolean;
}

interface RulesData {
  id: string;
  name: string;
  description: string;
  use: number;
  status: string;
  originField?: {
    id: string;
    name: string;
    selectionOptions?: string;
  };
  condition?: string;
  targetField?: {
    id: string;
    name: string;
    selectionOptions?: string;
  };
  action?: string;
  value?: string;
  createdAt?: string;
}

// Map GraphQL enums to UI-friendly options
const conditionOptions = [
  {
    key: RulesRuleConditionChoices.HasContent,
    label: "Tiene Contenido",
  },
  {
    key: RulesRuleConditionChoices.ContentEqualTo,
    label: "Contenido igual a",
  },
  {
    key: RulesRuleConditionChoices.ContentUnequalTo,
    label: "Contenido no igual a",
  },
  {
    key: RulesRuleConditionChoices.IsCompleted,
    label: "Estado completado 🟢",
  },
  {
    key: RulesRuleConditionChoices.IsNotCompleted,
    label: "Estado no completado 🔴",
  },
  {
    key: RulesRuleConditionChoices.InProgress,
    label: "Estado en progreso 🟡",
  },
];

// Action options
const actionOptions = [
  { key: RulesRuleActionChoices.Enable, label: "Habilitar" },
  { key: RulesRuleActionChoices.Disable, label: "Deshabilitar" },
];

// Helper functions to convert between frontend enums and backend string values
const convertEnumToBackendValue = (
  enumValue: string,
  _enumType: "action" | "condition" | "status",
): string => {
  // The GraphQL schema expects the enum values as-is (uppercase)
  // No conversion needed since we're already using the correct enum values
  return enumValue;
};

const convertBackendValueToEnum = (
  backendValue: string,
  _enumType: "action" | "condition" | "status",
): string => {
  // The backend returns enum values as-is (uppercase)
  // No conversion needed since the values are already in the correct format
  return backendValue;
};

export default function Rules({
  isCreating,
  setIsCreating,
  canEditConfiguration,
}: RulesModalProps) {
  const { rowCount } = useRowCountStore();
  const [page, setPage] = useState(1);
  const rowsPerPage = rowCount;

  // GraphQL queries
  const {
    data: rulesData,
    loading: rulesLoading,
    error: rulesError,
    refetch: refetchRules,
  } = useQuery(AllRulesDocument);
  const { data: fieldsData, loading: fieldsLoading } =
    useQuery(AllFieldsDocument);
  const [createRule, { loading: createLoading }] = useMutation(
    ExecuteCreateRuleDocument,
  );
  const [updateRule, { loading: updateLoading }] = useMutation(
    ExecuteUpdateRuleDocument,
  );

  // Modal states
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [selectedRule, setSelectedRule] = useState<RulesData | null>(null);

  // States for the table data
  const [tableData, setTableData] = useState<RulesData[]>([]);

  // Form states for create/edit
  const [newRule, setNewRule] = useState<{
    name: string;
    description: string;
    status: string;
    originFieldId: string;
    condition: string;
    targetFieldId: string;
    action: string;
    value: string;
  }>({
    name: "",
    description: "",
    status: RulesRuleStatusChoices.Active,
    originFieldId: "",
    condition: "",
    targetFieldId: "",
    action: "",
    value: "",
  });

  // Selected condition type to control value field visibility
  const [needsValueInput, setNeedsValueInput] = useState(false);

  // Search filter state
  const [searchTerm, setSearchTerm] = useState("");

  // Update table data when GraphQL data loads
  useEffect(() => {
    if (rulesData?.allRules) {
      const transformedRules: RulesData[] = rulesData.allRules.map(
        (rule: any) => ({
          id: rule.id,
          name: rule.name,
          description: rule.description || "",
          use: rule.templateCount,
          status: convertBackendValueToEnum(rule.status, "status"),
          originField: rule.originField,
          condition: convertBackendValueToEnum(rule.condition, "condition"),
          targetField: rule.targetField,
          action: convertBackendValueToEnum(rule.action, "action"),
          value: rule.value || "",
          createdAt: rule.createdAt || "",
        }),
      );

      setTableData(transformedRules);
    }
  }, [rulesData]);

  // Filtering and sorting states
  const [activeFilters, setActiveFilters] = useState<Record<string, string[]>>(
    {},
  );
  const [sortConfig, setSortConfig] = useState<{
    column: string;
    direction: "asc" | "desc";
  } | null>(null);

  const getUniqueValues = (column: keyof RulesData) => {
    return Array.from(new Set(tableData.map((rule) => String(rule[column]))));
  };

  const handleFilterChange = (column: string, selectedValues: string[]) => {
    const newFilters = {
      ...activeFilters,
      [column]: selectedValues,
    };

    if (selectedValues.length === 0) {
      delete newFilters[column];
    }

    setActiveFilters(newFilters);
  };

  const handleSort = (column: string, direction: "asc" | "desc") => {
    setSortConfig({ column, direction });
  };

  // Filter and paginate table data
  const filteredData = React.useMemo(() => {
    if (
      !searchTerm.trim() &&
      Object.keys(activeFilters).length === 0 &&
      !sortConfig
    )
      return tableData;

    let filtered = tableData;

    // Apply search filter
    if (searchTerm.trim()) {
      const lowerSearchTerm = searchTerm.toLowerCase();

      filtered = filtered.filter(
        (rule) =>
          rule.name.toLowerCase().includes(lowerSearchTerm) ||
          rule.description.toLowerCase().includes(lowerSearchTerm),
      );
    }

    // Apply column filters
    Object.entries(activeFilters).forEach(([column, values]) => {
      if (values.length > 0) {
        filtered = filtered.filter((rule) =>
          values.includes(String(rule[column as keyof RulesData])),
        );
      }
    });

    // Apply sorting
    if (sortConfig) {
      filtered = [...filtered].sort((a, b) => {
        const aValue = String(a[sortConfig.column as keyof RulesData]);
        const bValue = String(b[sortConfig.column as keyof RulesData]);

        if (sortConfig.direction === "asc") {
          return aValue.localeCompare(bValue);
        } else {
          return bValue.localeCompare(aValue);
        }
      });
    }

    return filtered;
  }, [tableData, searchTerm, activeFilters, sortConfig]);

  const pages = Math.ceil(filteredData.length / rowsPerPage);
  const items = React.useMemo(() => {
    const start = (page - 1) * rowsPerPage;
    const end = start + rowsPerPage;

    return filteredData.slice(start, end);
  }, [page, filteredData]);

  // Get field options from GraphQL data, grouped by type
  const fieldOptions = React.useMemo(() => {
    if (!fieldsData?.allFields) return [];

    return fieldsData.allFields.map((field: FieldType) => ({
      key: field.id,
      label: field.name,
      type: field.type,
      selectionOptions: field.selectionOptions,
      phase: field.subphase?.phase?.name || "default",
    }));
  }, [fieldsData]);

  // Group fields by type for sectioned autocomplete
  const fieldsByType = React.useMemo(() => {
    const grouped = fieldOptions.reduce(
      (acc: { [x: string]: any[] }, field: { type: string }) => {
        const type = field.type || "other";

        if (!acc[type]) {
          acc[type] = [];
        }

        acc[type].push(field);

        return acc;
      },
      {} as Record<string, typeof fieldOptions>,
    );

    return grouped;
  }, [fieldOptions]);

  // Get the selected origin field details
  const selectedOriginField = React.useMemo(() => {
    return fieldOptions.find(
      (field: { key: string }) => field.key === newRule.originFieldId,
    );
  }, [fieldOptions, newRule.originFieldId]);

  // Status options for task and document fields
  const taskStatusOptions = [
    { key: "pendiente", label: "Pendiente" },
    { key: "en_progreso", label: "En progreso" },
    { key: "completado", label: "Completado" },
    { key: "no_aplica", label: "N/A" },
  ];

  // Get value options based on field type and condition
  const getValueOptions = React.useMemo(() => {
    if (!selectedOriginField || !needsValueInput) return [];

    const fieldType = selectedOriginField.type;

    // For task and document fields, return status options
    if (fieldType === "task" || fieldType === "document") {
      return taskStatusOptions;
    }

    // For selection fields, parse and return selection options
    if (fieldType === "selection" && selectedOriginField.selectionOptions) {
      try {
        const options = JSON.parse(selectedOriginField.selectionOptions);

        if (Array.isArray(options)) {
          return options.map((option: any, index: number) => ({
            key: option.text || `option_${index}`,
            label: option.text || `Opción ${index + 1}`,
          }));
        }
      } catch {
        // Log error for debugging but don't break the UI
        // console.error("Error parsing selection options:", error);
      }
    }

    return [];
  }, [selectedOriginField, needsValueInput]);

  // Helper function to reset form data
  const resetFormData = () => {
    setNewRule({
      name: "",
      description: "",
      status: RulesRuleStatusChoices.Active,
      originFieldId: "",
      condition: "",
      targetFieldId: "",
      action: "",
      value: "",
    });
    setNeedsValueInput(false);
  };

  // Effect to reset form data when create modal opens
  useEffect(() => {
    if (isCreating) {
      resetFormData();
    }
  }, [isCreating]);

  // Effect to check if the selected condition needs a value input
  useEffect(() => {
    const valueNeededConditions = [
      RulesRuleConditionChoices.ContentEqualTo,
      RulesRuleConditionChoices.ContentUnequalTo,
    ];

    setNeedsValueInput(
      valueNeededConditions.includes(newRule.condition as any),
    );
  }, [newRule.condition]);

  const handleRowClick = (rule: RulesData) => {
    setSelectedRule(rule);
    setIsViewModalOpen(true);
  };

  const handleEdit = (rule: RulesData, _e: React.MouseEvent | any) => {
    setSelectedRule(rule);
    setNewRule({
      name: rule.name,
      description: rule.description,
      status: rule.status, // Already converted to enum format in useEffect
      originFieldId: rule.originField?.id || "",
      condition: rule.condition || "", // Already converted to enum format in useEffect
      targetFieldId: rule.targetField?.id || "",
      action: rule.action || "", // Already converted to enum format in useEffect
      value: rule.value || "",
    });
    setIsEditModalOpen(true);
  };

  const handleDelete = (rule: RulesData, _e: React.MouseEvent | any) => {
    setSelectedRule(rule);
    setIsDeleteModalOpen(true);
  };

  const handleSaveCreate = async () => {
    try {
      // Validate required fields
      if (!newRule.name.trim()) {
        // console.error("Rule name is required");
        return;
      }
      if (!newRule.originFieldId) {
        // console.error("Origin field is required");
        return;
      }
      if (!newRule.condition) {
        // console.error("Condition is required");
        return;
      }
      if (!newRule.targetFieldId) {
        // console.error("Target field is required");
        return;
      }
      if (!newRule.action) {
        // console.error("Action is required");
        return;
      }

      // Validate value field if needed
      if (needsValueInput && (!newRule.value || newRule.value.trim() === "")) {
        // console.error("Value is required for this condition");
        return;
      }

      // Build the rule input object, only including value if it's needed and has content
      const ruleInput: any = {
        name: newRule.name.trim(),
        description: newRule.description.trim(),
        status: convertEnumToBackendValue(newRule.status, "status"),
        originFieldId: newRule.originFieldId,
        condition: convertEnumToBackendValue(newRule.condition, "condition"),
        targetFieldId: newRule.targetFieldId,
        action: convertEnumToBackendValue(newRule.action, "action"),
      };

      // Only include value if the condition requires it and it has actual content
      if (needsValueInput && newRule.value && newRule.value.trim() !== "") {
        ruleInput.value = newRule.value.trim();
      }

      // console.log("Creating rule with data:", ruleInput);

      const result = await createRule({
        variables: {
          ruleData: ruleInput,
        },
      });

      if (result.data?.createRule?.rule) {
        await refetchRules();
        // console.log("Rule created successfully:", result.data.createRule.rule);
        resetFormData(); // Reset form after successful creation
      }

      setIsCreating(false);
    } catch {
      // console.error("Error creating rule:", error);
    }
  };

  const handleSaveEdit = async () => {
    if (!selectedRule) return;

    try {
      // Validate required fields
      if (!newRule.name.trim()) {
        // console.error("Rule name is required");

        return;
      }

      // console.log("Updating rule with data:", {
      //   id: selectedRule.id,
      //   name: newRule.name.trim(),
      //   description: newRule.description.trim(),
      //   status: newRule.status,
      // });

      const result = await updateRule({
        variables: {
          id: selectedRule.id,
          name: newRule.name.trim(),
          description: newRule.description.trim(),
          status: convertEnumToBackendValue(newRule.status, "status"),
        },
      });

      if (result.data?.updateRule?.rule) {
        await refetchRules();
        // console.log("Rule updated successfully:", result.data.updateRule.rule);
        setIsEditModalOpen(false);
      }
    } catch {
      // console.error("Error updating rule:", error);
    }
  };

  const handleConfirmDelete = () => {
    if (!selectedRule) return;

    // Remove the rule from the table data
    const updatedData = tableData.filter((rule) => rule.id !== selectedRule.id);

    setTableData(updatedData);
    setIsDeleteModalOpen(false);
  };

  // Helper function to get label from key for fields, conditions, and actions
  const getLabelFromKey = (
    key: string | undefined,
    optionsArray: { key: string; label: string }[],
  ) => {
    if (!key) return "";
    const option = optionsArray.find((item) => item.key === key);

    return option ? option.label : key;
  };

  const getStatusText = (status: string) => {
    return status === RulesRuleStatusChoices.Active ? "Activo" : "Inactivo";
  };

  // Helper function to get phase color
  const getPhaseColor = (phase: string) => {
    const phaseKey = phase.toLowerCase() as keyof typeof phaseColors;
    const color = phaseColors[phaseKey] || phaseColors.default;

    return {
      backgroundColor: color.bg,
      color: color.text,
      borderColor: color.border,
    };
  };

  // Show loading state
  // if (rulesLoading || fieldsLoading) {
  //   return (
  //     <div className="flex justify-center items-center h-64">
  //       <Spinner size="lg" />
  //     </div>
  //   );
  // }

  // Show error state
  if (rulesError) {
    return (
      <div className="flex justify-center items-center h-64">
        <p className="text-danger">Error loading rules: {rulesError.message}</p>
      </div>
    );
  }

  return (
    <div className="pt-4 w-full">
      <div className="flex w-full mb-4">
        <Input
          isClearable
          className="w-full"
          placeholder="Buscar regla..."
          startContent={
            <Icon
              className="text-default-400"
              icon="lucide:search"
              width={18}
            />
          }
          value={searchTerm}
          onValueChange={setSearchTerm}
        />
      </div>

      <Table
        removeWrapper
        aria-label="Tabla de reglas"
        bottomContent={
          pages > 0 ? (
            <div className="flex w-full justify-center">
              <Pagination
                isCompact
                showControls
                showShadow
                color="primary"
                page={page}
                total={pages}
                onChange={(page) => setPage(page)}
              />
            </div>
          ) : null
        }
      >
        <TableHeader>
          <TableColumn className="cursor-pointer">
            <FilterDropdown
              activeFilters={activeFilters}
              column="id"
              items={getUniqueValues("id")}
              sortConfig={sortConfig}
              title="#"
              onFilter={handleFilterChange}
              onSort={handleSort}
            />
          </TableColumn>
          <TableColumn className="cursor-pointer">
            <FilterDropdown
              activeFilters={activeFilters}
              column="name"
              items={getUniqueValues("name")}
              sortConfig={sortConfig}
              title="Nombre"
              onFilter={handleFilterChange}
              onSort={handleSort}
            />
          </TableColumn>
          <TableColumn className="cursor-pointer">
            <FilterDropdown
              activeFilters={activeFilters}
              column="description"
              items={getUniqueValues("description")}
              sortConfig={sortConfig}
              title="Descripción"
              onFilter={handleFilterChange}
              onSort={handleSort}
            />
          </TableColumn>
          <TableColumn className="cursor-pointer">
            <FilterDropdown
              activeFilters={activeFilters}
              column="use"
              items={getUniqueValues("use")}
              number={true}
              sortConfig={sortConfig}
              title="Plantillas"
              onFilter={handleFilterChange}
              onSort={handleSort}
            />
          </TableColumn>
          <TableColumn className="cursor-pointer">
            <FilterDropdown
              activeFilters={activeFilters}
              column="createdAt"
              items={getUniqueValues("createdAt")}
              number={true}
              sortConfig={sortConfig}
              title="Fecha creación"
              onFilter={handleFilterChange}
              onSort={handleSort}
            />
          </TableColumn>
          <TableColumn className="cursor-pointer">
            <FilterDropdown
              activeFilters={activeFilters}
              column="status"
              items={getUniqueValues("status")}
              sortConfig={sortConfig}
              title="Estado"
              onFilter={handleFilterChange}
              onSort={handleSort}
            />
          </TableColumn>
          <TableColumn>Acciones</TableColumn>
        </TableHeader>
        <TableBody
          isLoading={rulesLoading || fieldsLoading}
          items={items}
          loadingContent={<Spinner label="Cargando reglas..." />}
        >
          {(item) => (
            <TableRow
              key={item.id}
              className="cursor-pointer hover:bg-default-100"
              onClick={() => handleRowClick(item)}
            >
              <TableCell>{item.id}</TableCell>
              <TableCell>{item.name}</TableCell>
              <TableCell>{item.description}</TableCell>
              <TableCell>{item.use}</TableCell>
              <TableCell>
                {item.createdAt
                  ? new Date(item.createdAt).toISOString().split("T")[0]
                  : ""}
              </TableCell>
              <TableCell>
                <span
                  className={`px-2 py-1 rounded-full text-xs ${
                    item.status === RulesRuleStatusChoices.Active
                      ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-500"
                      : "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-500"
                  }`}
                >
                  {getStatusText(item.status)}
                </span>
              </TableCell>
              <TableCell>
                <div className="flex gap-2">
                  <Button
                    isIconOnly
                    color="primary"
                    isDisabled={
                      createLoading || updateLoading || !canEditConfiguration
                    }
                    size="sm"
                    variant="flat"
                    onPress={(e) => handleEdit(item, e)}
                  >
                    <Icon className="text-lg" icon="lucide:edit-3" />
                  </Button>
                  <Button
                    isIconOnly
                    color="danger"
                    isDisabled={
                      item.use > 1 ||
                      createLoading ||
                      updateLoading ||
                      !canEditConfiguration
                    }
                    size="sm"
                    variant="flat"
                    onPress={(e) => handleDelete(item, e)}
                  >
                    <Icon className="text-lg" icon="lucide:trash" />
                  </Button>
                </div>
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>

      {/* View Rule Modal */}
      <Modal
        isOpen={isViewModalOpen}
        // className="max-w-6xl"
        size="4xl"
        onClose={() => setIsViewModalOpen(false)}
      >
        <ModalContent>
          <ModalHeader>
            <h3 className="text-xl font-bold">Detalles de la Regla</h3>
          </ModalHeader>
          <ModalBody>
            {selectedRule && (
              <div className="space-y-4">
                <div className="flex flex-col space-y-1">
                  <span className="text-sm text-default-500">Nombre</span>
                  <span className="text-lg font-medium">
                    {selectedRule.name}
                  </span>
                </div>

                <div className="flex flex-col space-y-1">
                  <span className="text-sm text-default-500">Descripción</span>
                  <span>{selectedRule.description}</span>
                </div>

                <div className="flex flex-col space-y-1">
                  <span className="text-sm text-default-500">Estado</span>
                  <span
                    className={`px-2 py-1 rounded-full text-xs w-fit ${
                      selectedRule.status === RulesRuleStatusChoices.Active
                        ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-500"
                        : "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-500"
                    }`}
                  >
                    {getStatusText(selectedRule.status)}
                  </span>
                </div>

                <Card className="p-4 bg-default-50">
                  <h4 className="text-md font-semibold mb-2">
                    Definición de la Regla
                  </h4>
                  <p className="text-sm mb-4">
                    SI{" "}
                    <span className="font-medium">
                      {selectedRule.originField?.name || "Campo no encontrado"}
                    </span>{" "}
                    <span className="font-medium">
                      {getLabelFromKey(
                        selectedRule.condition,
                        conditionOptions,
                      )}
                    </span>
                    {selectedRule.value && (
                      <span>
                        {"s "}
                        &ldquo;
                        <span className="font-medium">
                          {selectedRule.value}
                        </span>
                        &rdquo;
                      </span>
                    )}
                    {" ENTONCES "}
                    <span className="font-medium">
                      {getLabelFromKey(selectedRule.action, actionOptions)}
                    </span>
                    {" el campo "}
                    <span className="font-medium">
                      {selectedRule.targetField?.name || "Campo no encontrado"}
                    </span>
                  </p>
                </Card>

                <div className="flex flex-col space-y-1">
                  <span className="text-sm text-default-500">Uso</span>
                  <span>
                    Esta regla se utiliza en {selectedRule.use}{" "}
                    {selectedRule.use === 1 ? "lugar" : "lugares"}
                  </span>
                </div>
              </div>
            )}
          </ModalBody>
          <ModalFooter>
            <Button color="primary" onPress={() => setIsViewModalOpen(false)}>
              Cerrar
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* Create Modal */}
      <Modal
        className="max-w-6xl"
        isDismissable={!createLoading}
        isKeyboardDismissDisabled={createLoading}
        isOpen={isCreating}
        size="4xl"
        onClose={() => {
          if (!createLoading) {
            resetFormData();
            setIsCreating(false);
          }
        }}
      >
        <ModalContent>
          <ModalHeader>
            <h3 className="text-xl font-bold">Crear Nueva Regla</h3>
          </ModalHeader>
          <ModalBody>
            <div className="flex flex-col gap-4">
              <Input
                isDisabled={createLoading}
                label="Nombre de la Regla"
                placeholder="Ingrese nombre de la regla"
                value={newRule.name}
                onValueChange={(value) =>
                  setNewRule({ ...newRule, name: value })
                }
              />
              <Input
                isDisabled={createLoading}
                label="Descripción"
                placeholder="Ingrese descripción de la regla"
                value={newRule.description}
                onValueChange={(value) =>
                  setNewRule({ ...newRule, description: value })
                }
              />

              <div className="border-t border-default-200 my-2 pt-4">
                <h4 className="text-md font-semibold mb-4">
                  Definición de la Regla
                </h4>

                <div className="flex flex-wrap items-center gap-2 rounded-lg">
                  <span className="font-medium">SI</span>
                  <Autocomplete
                    className="w-[275px]"
                    isDisabled={createLoading}
                    placeholder="Campo"
                    selectedKey={newRule.originFieldId}
                    variant="bordered"
                    onSelectionChange={(key) =>
                      setNewRule({ ...newRule, originFieldId: key as string })
                    }
                  >
                    {Object.entries(fieldsByType).map(([type, fields]) => (
                      <AutocompleteSection
                        key={type}
                        title={
                          {
                            informative: "Informativo",
                            selection: "Selección",
                            task_with_subtasks: "Subtarea",
                            task: "Tarea",
                            document: "Documento",
                          }[type] ||
                          type.charAt(0).toUpperCase() + type.slice(1)
                        }
                      >
                        {(fields as typeof fieldOptions).map(
                          (option: {
                            key: React.Key | null | undefined;
                            label:
                              | string
                              | number
                              | bigint
                              | boolean
                              | React.ReactElement<
                                  any,
                                  string | React.JSXElementConstructor<any>
                                >
                              | Iterable<React.ReactNode>
                              | Promise<React.AwaitedReactNode>
                              | null
                              | undefined;
                          }) => (
                            <AutocompleteItem
                              key={option.key}
                              textValue={String(option.label)}
                            >
                              <div className="text-default-700">
                                {option.label}{" "}
                                <Chip
                                  size="sm"
                                  style={getPhaseColor((option as any).phase)}
                                  variant="flat"
                                >
                                  {typeof (option as any).phase === "string"
                                    ? (option as any).phase.toLowerCase() ===
                                      "incubadora"
                                      ? "TAKE OFF"
                                      : (option as any).phase
                                    : (option as any).phase}
                                </Chip>
                              </div>
                            </AutocompleteItem>
                          ),
                        )}
                      </AutocompleteSection>
                    ))}
                  </Autocomplete>

                  <Select
                    className="w-[250px]"
                    isDisabled={createLoading}
                    placeholder="Condición"
                    selectedKeys={newRule.condition ? [newRule.condition] : []}
                    onChange={(e) =>
                      setNewRule({ ...newRule, condition: e.target.value })
                    }
                  >
                    {conditionOptions.map((option) => (
                      <SelectItem key={option.key}>{option.label}</SelectItem>
                    ))}
                  </Select>

                  {needsValueInput && (
                    <>
                      {getValueOptions.length > 0 ? (
                        <Select
                          className="w-[150px]"
                          isDisabled={createLoading}
                          placeholder="Valor"
                          selectedKeys={newRule.value ? [newRule.value] : []}
                          onChange={(e) =>
                            setNewRule({ ...newRule, value: e.target.value })
                          }
                        >
                          {getValueOptions.map((option) => (
                            <SelectItem key={option.key}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </Select>
                      ) : (
                        <Input
                          className="w-[150px]"
                          isDisabled={createLoading}
                          placeholder="Valor"
                          value={newRule.value}
                          onValueChange={(value) =>
                            setNewRule({ ...newRule, value: value })
                          }
                        />
                      )}
                    </>
                  )}

                  <div className="w-full" />

                  <span className="font-medium">ENTONCES</span>

                  <Select
                    className="w-[197px]"
                    isDisabled={createLoading}
                    placeholder="Acción"
                    selectedKeys={newRule.action ? [newRule.action] : []}
                    onChange={(e) =>
                      setNewRule({ ...newRule, action: e.target.value })
                    }
                  >
                    {actionOptions.map((option) => (
                      <SelectItem key={option.key}>{option.label}</SelectItem>
                    ))}
                  </Select>

                  <Autocomplete
                    className="w-[250px]"
                    isDisabled={createLoading}
                    placeholder="Campo Destino"
                    selectedKey={newRule.targetFieldId}
                    variant="bordered"
                    onSelectionChange={(key) =>
                      setNewRule({ ...newRule, targetFieldId: key as string })
                    }
                  >
                    {Object.entries(fieldsByType).map(([type, fields]) => (
                      <AutocompleteSection
                        key={type}
                        title={
                          {
                            informative: "Informativo",
                            selection: "Selección",
                            task_with_subtasks: "Subtarea",
                            task: "Tarea",
                            document: "Documento",
                          }[type] ||
                          type.charAt(0).toUpperCase() + type.slice(1)
                        }
                      >
                        {(fields as typeof fieldOptions).map(
                          (option: {
                            key: React.Key | null | undefined;
                            label:
                              | string
                              | number
                              | bigint
                              | boolean
                              | React.ReactElement<
                                  any,
                                  string | React.JSXElementConstructor<any>
                                >
                              | Iterable<React.ReactNode>
                              | Promise<React.AwaitedReactNode>
                              | null
                              | undefined;
                          }) => (
                            <AutocompleteItem
                              key={option.key}
                              textValue={String(option.label)}
                            >
                              <div className="text-default-700">
                                {option.label}{" "}
                                <Chip
                                  size="sm"
                                  style={getPhaseColor((option as any).phase)}
                                  variant="flat"
                                >
                                  {typeof (option as any).phase === "string"
                                    ? (option as any).phase.toLowerCase() ===
                                      "incubadora"
                                      ? "TAKE OFF"
                                      : (option as any).phase
                                    : (option as any).phase}
                                </Chip>
                              </div>
                            </AutocompleteItem>
                          ),
                        )}
                      </AutocompleteSection>
                    ))}
                  </Autocomplete>
                </div>
              </div>

              <Select
                className="mt-4"
                isDisabled={createLoading}
                label="Estado"
                selectedKeys={[newRule.status || RulesRuleStatusChoices.Active]}
                onChange={(e) =>
                  setNewRule({ ...newRule, status: e.target.value })
                }
              >
                <SelectItem key={RulesRuleStatusChoices.Active}>
                  Activo
                </SelectItem>
                <SelectItem key={RulesRuleStatusChoices.Inactive}>
                  Inactivo
                </SelectItem>
              </Select>
            </div>
          </ModalBody>
          <ModalFooter>
            <Button
              color="danger"
              isDisabled={createLoading}
              variant="light"
              onPress={() => {
                if (!createLoading) {
                  resetFormData();
                  setIsCreating(false);
                }
              }}
            >
              Cancelar
            </Button>
            <Button
              color="primary"
              isLoading={createLoading}
              onPress={handleSaveCreate}
            >
              Crear Regla
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* Edit Modal */}
      <Modal
        className="max-w-6xl"
        isDismissable={!updateLoading}
        isKeyboardDismissDisabled={updateLoading}
        isOpen={isEditModalOpen}
        size="4xl"
        onClose={() => {
          if (!updateLoading) {
            setIsEditModalOpen(false);
          }
        }}
      >
        <ModalContent>
          <ModalHeader>
            <h3 className="text-xl font-bold">Editar Regla</h3>
          </ModalHeader>
          <ModalBody>
            <div className="flex flex-col gap-4">
              <Input
                isDisabled={updateLoading}
                label="Nombre de la Regla"
                placeholder="Ingrese nombre de la regla"
                value={newRule.name}
                onValueChange={(value) =>
                  setNewRule({ ...newRule, name: value })
                }
              />
              <Input
                isDisabled={updateLoading}
                label="Descripción"
                placeholder="Ingrese descripción de la regla"
                value={newRule.description}
                onValueChange={(value) =>
                  setNewRule({ ...newRule, description: value })
                }
              />

              <div className="border-t border-default-200 my-2 pt-4">
                <h4 className="text-md font-semibold mb-4">
                  Definición de la Regla
                </h4>

                <div className="flex flex-wrap items-center gap-2 rounded-lg">
                  <span className="font-medium">SI</span>
                  <Select
                    className="w-[275px]"
                    isDisabled={true}
                    placeholder="Campo"
                    selectedKeys={
                      newRule.originFieldId ? [newRule.originFieldId] : []
                    }
                    onChange={(e) =>
                      setNewRule({ ...newRule, originFieldId: e.target.value })
                    }
                  >
                    {fieldOptions.map(
                      (option: { key: string; label: string }) => (
                        <SelectItem key={option.key}>{option.label}</SelectItem>
                      ),
                    )}
                  </Select>

                  <Select
                    className="w-[160px]"
                    isDisabled={true}
                    placeholder="Condición"
                    selectedKeys={newRule.condition ? [newRule.condition] : []}
                    onChange={(e) =>
                      setNewRule({ ...newRule, condition: e.target.value })
                    }
                  >
                    {conditionOptions.map((option) => (
                      <SelectItem key={option.key}>{option.label}</SelectItem>
                    ))}
                  </Select>

                  {needsValueInput && (
                    <Input
                      className="w-[150px]"
                      isDisabled={true}
                      placeholder="Valor"
                      value={newRule.value}
                      onValueChange={(value) =>
                        setNewRule({ ...newRule, value: value })
                      }
                    />
                  )}

                  <span className="font-medium">ENTONCES</span>

                  <Select
                    className="w-[180px]"
                    isDisabled={true}
                    placeholder="Acción"
                    selectedKeys={newRule.action ? [newRule.action] : []}
                    onChange={(e) =>
                      setNewRule({ ...newRule, action: e.target.value })
                    }
                  >
                    {actionOptions.map((option) => (
                      <SelectItem key={option.key}>{option.label}</SelectItem>
                    ))}
                  </Select>

                  <Select
                    className="w-[180px]"
                    isDisabled={true}
                    placeholder="Campo Destino"
                    selectedKeys={
                      newRule.targetFieldId ? [newRule.targetFieldId] : []
                    }
                    onChange={(e) =>
                      setNewRule({ ...newRule, targetFieldId: e.target.value })
                    }
                  >
                    {fieldOptions.map(
                      (option: { key: string; label: string }) => (
                        <SelectItem key={option.key}>{option.label}</SelectItem>
                      ),
                    )}
                  </Select>
                </div>
              </div>

              <Select
                className="mt-4"
                isDisabled={updateLoading}
                label="Estado"
                selectedKeys={[newRule.status || RulesRuleStatusChoices.Active]}
                onChange={(e) =>
                  setNewRule({ ...newRule, status: e.target.value })
                }
              >
                <SelectItem key={RulesRuleStatusChoices.Active}>
                  Activo
                </SelectItem>
                <SelectItem key={RulesRuleStatusChoices.Inactive}>
                  Inactivo
                </SelectItem>
              </Select>
            </div>
          </ModalBody>
          <ModalFooter>
            <Button
              color="danger"
              isDisabled={updateLoading}
              variant="light"
              onPress={() => {
                if (!updateLoading) {
                  setIsEditModalOpen(false);
                }
              }}
            >
              Cancelar
            </Button>
            <Button
              color="primary"
              isLoading={updateLoading}
              onPress={handleSaveEdit}
            >
              Guardar Cambios
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* Delete Modal */}
      <Modal
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
      >
        <ModalContent>
          <ModalHeader>
            <h3 className="text-xl font-bold">Confirmar Eliminación</h3>
          </ModalHeader>
          <ModalBody>
            <p>
              ¿Está seguro de que desea eliminar la regla{" "}
              <span className="font-semibold">{selectedRule?.name}</span>?
              {selectedRule?.use === 0 ? (
                <span className="block mt-2 text-gray-600">
                  Esta regla no está siendo utilizada y puede ser eliminada de
                  forma segura.
                </span>
              ) : (
                <span className="block mt-2 text-amber-600">
                  Advertencia: Esta regla se utiliza en {selectedRule?.use}{" "}
                  {selectedRule?.use === 1 ? "lugar" : "lugares"}.
                </span>
              )}
            </p>
          </ModalBody>
          <ModalFooter>
            <Button
              color="danger"
              variant="light"
              onPress={() => setIsDeleteModalOpen(false)}
            >
              Cancelar
            </Button>
            <Button color="danger" onPress={handleConfirmDelete}>
              Eliminar Regla
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
}
