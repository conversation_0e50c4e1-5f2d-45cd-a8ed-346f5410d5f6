import React, { useState } from "react";
import { Button } from "@heroui/react";
import { Icon } from "@iconify/react";

interface Step {
  key: number;
  title: string;
  description: string;
}

interface ReportStepperProps {
  steps: Step[];
  currentStep: number;
  onStepClick: (step: number) => void;
}

export default function ReportStepper({
  steps,
  currentStep,
  onStepClick,
}: ReportStepperProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <div className="w-full mb-4 relative overflow-hidden">
      {/* Toggle Button */}
      <Button
        isIconOnly
        className="absolute bottom-2 right-2 z-10 bg-transparent hover:bg-default-100 transition-all duration-200"
        size="sm"
        variant="light"
        onPress={() => setIsExpanded(!isExpanded)}
      >
        <Icon
          className={`transition-transform duration-300 ease-in-out ${
            isExpanded ? "rotate-180" : "rotate-0"
          }`}
          icon="heroicons:chevron-down"
          width={14}
        />
      </Button>

      {/* Compact Stepper View */}
      <div className="flex items-center justify-between py-4">
        {steps.map((step, index) => {
          const isActive = step.key === currentStep;
          const isCompleted = step.key < currentStep;
          const isClickable = step.key <= currentStep;

          return (
            <React.Fragment key={step.key}>
              {/* Step Circle and Info */}
              <div className="flex flex-col items-center">
                <Button
                  isIconOnly
                  className={`w-10 h-10 rounded-full border-2 transition-all duration-200 mb-2 ${
                    isActive
                      ? "bg-primary border-primary text-primary-foreground shadow-md"
                      : isCompleted
                        ? "bg-success border-success text-success-foreground shadow-sm"
                        : "bg-default-100 border-default-300 text-default-500"
                  } ${isClickable ? "cursor-pointer hover:scale-105" : "cursor-default"}`}
                  disabled={!isClickable}
                  onPress={() => isClickable && onStepClick(step.key)}
                >
                  {isCompleted ? (
                    <Icon icon="heroicons:check" width={16} />
                  ) : (
                    <span className="text-sm font-bold">{step.key}</span>
                  )}
                </Button>
                <div className="text-center">
                  <p className="text-sm font-medium text-default-700 mb-1">
                    {step.title}
                  </p>
                  {isExpanded && (
                    <p className="text-xs text-default-500 max-w-32 leading-tight">
                      {step.description}
                    </p>
                  )}
                </div>
              </div>

              {/* Connecting Line */}
              {index < steps.length - 1 && (
                <div className="flex-1 mx-4">
                  <div
                    className={`h-0.5 transition-all duration-300 ${
                      step.key < currentStep ? "bg-success" : "bg-default-300"
                    }`}
                  />
                </div>
              )}
            </React.Fragment>
          );
        })}
      </div>
    </div>
  );
}
