import { useRouter } from "next/navigation";
import { useEffect } from "react";

interface ReportsProps {
  isCreating: boolean;
  setIsCreating: (value: boolean) => void;
}

export default function Reports({ isCreating, setIsCreating }: ReportsProps) {
  const router = useRouter();

  useEffect(() => {
    if (!isCreating) return;
    console.log("Creating report...");
    setIsCreating(false);
    router.push("/configuracion/reportes/crear");
  }, [isCreating]);

  return <div>Reports</div>;
}
